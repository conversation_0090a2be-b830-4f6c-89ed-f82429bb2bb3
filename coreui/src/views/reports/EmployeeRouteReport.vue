  <template>
    <div>
      <c-card>
        <c-card-header> Employee Route Report </c-card-header>
        <c-card-body>
          <div class="form-row form-group">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label>
                  <strong>Line</strong>
                </template>
                <template #input>
                  <v-select v-model="line_id" :options="lines" label="name" :value="0" required
                    :reduce="(line) => line.id" placeholder="Select Line" class="mt-2" @input="getLineEmployees()" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label>
                  <strong>Employee</strong>
                </template>
                <template #input>
                  <v-select required v-model="users_id" :options="users" :reduce="(user) => user.id" label="fullname"
                    :value="0" placeholder="Select Employee" class="mt-2" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template #label>
                  <strong>Date</strong>
                </template>
                <template #input>
                  <c-input type="date" placeholder="Date" v-model="date" class="mt-2"></c-input>
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="show()" style="float: right">Show</c-button>
        </c-card-footer>
      </c-card>
      <c-card v-if="showTable" class="mt-5">
        <c-card-header class="d-flex justify-content-between align-items-center">
          <span>Employee Route Report with Manager Routes</span>
          <div class="d-flex align-items-center">
            <c-button
              size="sm"
              :color="showManagerRoutes ? 'success' : 'secondary'"
              @click="toggleManagerRoutes"
              class="mr-2"
            >
              {{ showManagerRoutes ? 'Hide' : 'Show' }} Manager Routes
            </c-button>
            <c-button
              size="sm"
              color="info"
              @click="showLegend = !showLegend"
            >
              {{ showLegend ? 'Hide' : 'Show' }} Legend
            </c-button>
          </div>
        </c-card-header>

        <!-- Legend -->
        <div v-if="showLegend" class="legend-container p-3 bg-light border-bottom">
          <h6 class="mb-3">Route Legend</h6>
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-primary">Employee Route</h6>
              <div class="legend-item d-flex align-items-center mb-2">
                <div class="legend-line" style="background-color: #099AC9; width: 30px; height: 3px; margin-right: 10px;"></div>
                <span>Employee Path</span>
              </div>
              <div class="legend-item d-flex align-items-center mb-2">
                <div class="legend-marker employee-marker"></div>
                <span>Employee Visits</span>
              </div>
            </div>
            <div class="col-md-6" v-if="showManagerRoutes && Object.keys(managerColors).length > 0">
              <h6 class="text-success">Manager Routes</h6>
              <div v-for="(color, managerId) in managerColors" :key="managerId" class="legend-item d-flex align-items-center mb-2">
                <div class="legend-line" :style="`background-color: ${color}; width: 30px; height: 3px; margin-right: 10px;`"></div>
                <div class="legend-marker" :style="`background-color: ${color};`"></div>
                <span class="ml-2">Manager {{ managerId }}</span>
              </div>
            </div>
          </div>
        </div>

        <c-card-body>
          <gmap-map ref="polylineMap" class="mt-3" :center="center" :zoom="10" style="width: 100%; height: 500px">
            <!-- Employee Route Polyline -->
            <gmap-polyline
              :path.sync="polyLinePath"
              :options="{ strokeColor: '#099AC9', strokeWeight: 3, strokeOpacity: 0.8 }"
            ></gmap-polyline>

            <!-- Manager Routes Polylines -->
            <template v-if="showManagerRoutes">
              <gmap-polyline
                v-for="(path, managerId) in managerPaths"
                :key="`manager-${managerId}`"
                :path="path"
                :options="{
                  strokeColor: managerColors[managerId],
                  strokeWeight: 2,
                  strokeOpacity: 0.7,
                  strokeDashArray: '10,5'
                }"
              ></gmap-polyline>
            </template>

            <gmap-cluster>
              <gmap-info-window
                :options="infoOptions"
                :position="infoWindowPos"
                :opened="infoWinOpen"
                @closeclick="infoWinOpen = false"
              >
                <div v-html="infoOptions.content"></div>
              </gmap-info-window>

              <!-- Employee Markers -->
              <gmap-marker
                v-for="(m, index) in markers"
                :key="`employee-${index}`"
                :position="m.position"
                :clickable="true"
                :icon="getEmployeeMarkerIcon(m)"
                @click="toggleInfoWindow(m, index, 'employee')"
              ></gmap-marker>

              <!-- Manager Markers -->
              <template v-if="showManagerRoutes">
                <gmap-marker
                  v-for="(marker, index) in allManagerMarkers"
                  :key="`manager-${marker.manager_id}-${index}`"
                  :position="marker.position"
                  :clickable="true"
                  :icon="getManagerMarkerIcon(marker)"
                  @click="toggleInfoWindow(marker, index, 'manager')"
                ></gmap-marker>
              </template>
            </gmap-cluster>
          </gmap-map>
          <!-- <c-list-group subheader>
          <c-subheader class="font-weight-bold">Steps</c-subheader>
          <c-list-group-item
            v-for="(address, index) in addresses"
            :key="index"
            class="flex align-center"
          > -->
          <!-- <c-list-group-item-icon>
              <v-badge class="mt-1" color="primary" :content="index + 1" />
            </c-list-group-item-icon> -->
          <!-- <c-list-group-item>{{ address }}</c-list-group-item>
          </c-list-group-item>
        </c-list-group> -->
        </c-card-body>
        <c-card-footer>
          <div class="totalCard col-3 mx-4" v-if="dist > 0">
            <strong>Total Route KM: {{ dist }} KM</strong> <br />
          </div>
        </c-card-footer>
      </c-card>
    </div>
  </template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      lines: [],
      line_id: null,
      users: [],
      users_id: null,
      date: null,
      showTable: false,
      showManagerRoutes: true,
      showLegend: true,
      center: { lat: 30.0444, lng: 31.2357 },
      edited: null,
      paths: [],
      originalPaths: [],
      loading: false,
      mvcPaths: null,
      errorMessage: null,
      polygonGeojson: null,
      addresses: [],
      markers: [],
      markerManagers: {},
      managerColors: {},
      managerPaths: {},
      allManagerMarkers: [],
      infoOptions: {
        content: '',
        pixelOffset: {
          width: 0,
          height: -35,
        },
      },
      infoWindowPos: null,
      infoWinOpen: true,
      currentMidx: null,
      dist: null,
      // Predefined colors for managers
      availableColors: [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
      ],
    };
  },
  computed: {
    polyLinePath() {
      return this.markers.map(({ position }) => position);
    },
  },
  methods: {
    initialize() {
      axios
        .get("/api/employee-route")
        .then((response) => {
          this.lines = response.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineEmployees() {
      axios
        .get(`/api/employee-route/${this.line_id}`)
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    show() {
      this.showTable = true;
      axios
        .post(`/api/employee-routes`, {
          line_id: this.line_id,
          user_id: this.users_id,
          date: this.crmDateFormat(this.date),
        })
        .then((response) => {
          this.markers = response.data.markers;
          this.markerManagers = response.data.markerManagers || {};
          this.dist = response.data.dist;

          // Process manager data
          this.processManagerData();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    toggleInfoWindow: function (marker, idx, type = 'employee') {
      this.center = marker.position;
      this.infoWindowPos = marker.position;
      this.infoOptions.content = this.getInfoWindowContent(marker, type);

      const currentKey = `${type}-${idx}`;

      // Check if it's the same marker that was selected
      if (this.currentMidx == currentKey) {
        this.infoWinOpen = !this.infoWinOpen;
      } else {
        this.infoWinOpen = true;
        this.currentMidx = currentKey;
      }
    },
    getInfoWindowContent(marker, type = 'employee') {
      const baseContent = `
        <div class="info-window-content">
          <div class="mb-2">
            <span class="badge badge-${type === 'manager' ? 'success' : 'primary'}">${type === 'manager' ? 'Manager' : 'Employee'} Visit</span>
          </div>
          <div class="m-2">
            <span style="font-weight: bold;">Visit ID: </span>
            ${marker.id}
          </div>
          <div class="m-2">
            <span style="font-weight: bold;">Account: </span>
            ${marker.account}
          </div>
          <div class="m-2">
            <span style="font-weight: bold;">Doctor: </span>
            ${marker.doctor}
          </div>
          <div class="m-2">
            <span style="font-weight: bold;">Date: </span>
            ${marker.date}
          </div>
          ${type === 'manager' ? `
            <div class="m-2">
              <span style="font-weight: bold;">Manager ID: </span>
              ${marker.manager_id}
            </div>
            <div class="m-2">
              <span style="font-weight: bold; color: ${this.managerColors[marker.manager_id]};">Route Color: </span>
              <span class="color-indicator" style="background-color: ${this.managerColors[marker.manager_id]}; width: 20px; height: 15px; display: inline-block; border-radius: 3px;"></span>
            </div>
          ` : ''}
        </div>
      `;
      return baseContent;
    },

    processManagerData() {
      this.managerColors = {};
      this.managerPaths = {};
      this.allManagerMarkers = [];

      let colorIndex = 0;

      // Process each manager's data
      Object.keys(this.markerManagers).forEach(managerKey => {
        const managerId = managerKey.replace('markerManager_', '');
        const managerMarkers = this.markerManagers[managerKey];

        if (managerMarkers && managerMarkers.length > 0) {
          // Assign color to manager
          this.managerColors[managerId] = this.availableColors[colorIndex % this.availableColors.length];
          colorIndex++;

          // Create path for manager
          this.managerPaths[managerId] = managerMarkers.map(marker => marker.position);

          // Add to all manager markers
          this.allManagerMarkers.push(...managerMarkers);
        }
      });
    },

    toggleManagerRoutes() {
      this.showManagerRoutes = !this.showManagerRoutes;
    },

    getEmployeeMarkerIcon(marker) {
      return {
        url: this.createMarkerIcon('#099AC9', 'E'),
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 40)
      };
    },

    getManagerMarkerIcon(marker) {
      const color = this.managerColors[marker.manager_id] || '#FF6B6B';
      return {
        url: this.createMarkerIcon(color, 'M'),
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 40)
      };
    },

    createMarkerIcon(color, text) {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = 40;
      canvas.height = 40;

      // Draw marker background
      context.fillStyle = color;
      context.beginPath();
      context.arc(20, 15, 12, 0, 2 * Math.PI);
      context.fill();

      // Draw marker point
      context.beginPath();
      context.moveTo(20, 27);
      context.lineTo(14, 20);
      context.lineTo(26, 20);
      context.closePath();
      context.fill();

      // Draw text
      context.fillStyle = 'white';
      context.font = 'bold 12px Arial';
      context.textAlign = 'center';
      context.fillText(text, 20, 20);

      return canvas.toDataURL();
    },

    getSteps() {
      this.markers.forEach(({ position }) => {
        this.getAddressOf(position);
      });
    },
    getAddressOf(location) {
      this.$refs.polylineMap.$mapPromise.then(() => {
        const geocoder = new google.maps.Geocoder();

        geocoder.geocode({ location }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK) {
            this.addresses.push(results[0].formatted_address);
          }
        });
      });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style>
.totalCard {
  height: 60px;
  float: left;
  border-radius: 5px;
  color: white;
  padding: 15px;
  background-color: brown;
  animation-name: totalCard;
  animation-duration: 4s;
}

/* Legend Styles */
.legend-container {
  border-left: 4px solid #007bff;
}

.legend-item {
  font-size: 14px;
  margin-bottom: 8px;
}

.legend-line {
  border-radius: 2px;
  margin-right: 10px;
}

.legend-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.employee-marker {
  background-color: #099AC9;
}

/* Info Window Styles */
.info-window-content {
  min-width: 200px;
  font-family: Arial, sans-serif;
}

.info-window-content .badge {
  font-size: 11px;
  padding: 4px 8px;
}

.color-indicator {
  border: 1px solid #ccc;
  margin-left: 5px;
}

/* Map Controls */
.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

.map-controls .btn {
  margin-left: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .legend-container .row {
    flex-direction: column;
  }

  .legend-container .col-md-6 {
    margin-bottom: 15px;
  }
}

/* Animation for route lines */
@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

.manager-route-line {
  animation: dash 1s linear infinite;
}
</style>