<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" :report-name="name"/>
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          {{ name }} <br/>
          from: {{ saleData.fromDate }} to: {{ saleData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
                      :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template v-for="column in fields" v-slot:[column]="{ item }">
            <td v-if="item.product === 'Total'"
                :style="{
                    color: 'white',
                    background: item.color,
                }"
            >
              <strong>{{ item[column] }}</strong>
            </td>
            <td v-else>
              <strong>{{ item[column] }}</strong>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
                  :data="items" :name="name"/>
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/salesIncentiveCalculations/filterData.vue";
import {Amiri} from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";

export default {
  components: {
    download,
    filterData,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      saleData: {},
      name: "Sales Incentive Report",
    };
  },
  emits: ["downloaded"],
  methods: {
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadXlsx(this.items, `${this.name}.xlsx`);
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, `${this.name}.csv`);
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name.replaceAll(' ', '');
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({filters: ["ASCIIHexEncode"]});

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: {top: 10},
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({saleFilter}) {
      this.saleData = saleFilter;
      if (saleFilter.type != 2) {
        axios
          .post(`/api/sales-incentive-report/`, {
            saleFilter,
          },{
            useSSEProgress: true,
          })
          .then((response) => {
            this.items = response.data.data;
            this.fields = response.data.fields;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      } else {
        axios
          .post(`/api/sales-incentive-perbrand-report/`, {
            saleFilter,
          })
          .then((response) => {
            this.items = response.data.data;
            this.fields = response.data.fields;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }

    },
  },
};
</script>
