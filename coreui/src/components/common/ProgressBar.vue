<template>
  <div
    v-if="dialog"
    class="progress-overlay"
    :style="{ zIndex: options.zIndex }"
  >
    <div class="progress-modal" :style="{ maxWidth: options.width + 'px' }">
      <!-- Header with gradient background -->
      <div class="progress-header">
        <div class="progress-icon">
          <div class="spinner" v-if="progressData.progress === 0"></div>
          <svg v-else class="checkmark-icon" viewBox="0 0 24 24" width="24" height="24">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
            <path d="M8 12l2 2 4-4" fill="none" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h4 class="progress-title">
          {{ progressData.message || 'Loading...' }}
        </h4>
      </div>

      <!-- Progress Section -->
      <div class="progress-content">
        <!-- Progress Bar Container -->
        <div class="progress-bar-container">
          <div class="progress-bar-track">
            <div
              class="progress-bar-fill"
              :class="{ 'indeterminate': progressData.progress === 0 }"
              :style="{ width: progressData.progress > 0 ? progressData.progress + '%' : '0%' }"
            >
              <div class="progress-bar-shine"></div>
            </div>
          </div>
        </div>

        <!-- Progress Text -->
        <div class="progress-text">
          <div class="progress-percentage" v-if="progressData.progress > 0">
            {{ Math.ceil(progressData.progress) }}%
          </div>
          <div class="progress-status" v-if="progressData.progress > 0">
            {{ Math.ceil(progressData.progress) }}% complete
          </div>
          <div class="progress-status" v-else>
            Processing...
          </div>
        </div>

        <!-- Additional Info -->
        <div class="progress-info" v-if="progressData.estimatedTime || progressData.elapsedTime">
          <div class="time-info">
            <span v-if="progressData.elapsedTime" class="elapsed-time">
              Elapsed: {{ formatTime(progressData.elapsedTime) }}
            </span>
            <span v-if="progressData.estimatedTime" class="estimated-time">
              Remaining: {{ formatTime(progressData.estimatedTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { mapState, mapActions } from "vuex";
export default {
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    options: {
      color: "primary",
      width: 350, // Increased width to accommodate progress text
      zIndex: 1000000000,
    },
  }),
  computed:{
    ...mapState("app", ["progressBar", "progressData"])
  },
  methods: {
    ...mapActions("app", ["loadVApp", "unLoadVApp"]),
    open(options) {
      this.loadVApp()
      this.dialog = true;
      this.options = Object.assign(this.options, options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      })
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp()
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp()
    },
    formatTime(seconds) {
      if (!seconds || seconds < 0) return '0s';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
      } else {
        return `${secs}s`;
      }
    },
  },
  watch:{
    progressBar(newVal) {
      this.dialog = newVal
      if(newVal) {
        this.loadVApp()
        return
      }
      this.unLoadVApp()
    }
  },
};
</script>

<style scoped>
/* Overlay */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal */
.progress-modal {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-width: 400px;
  max-width: 90vw;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header */
.progress-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
}

.progress-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.progress-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.checkmark-icon {
  color: white;
  animation: checkmarkPulse 0.6s ease-out;
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.progress-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Content */
.progress-content {
  padding: 32px 24px 24px;
}

/* Progress Bar */
.progress-bar-container {
  margin-bottom: 20px;
}

.progress-bar-track {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 4px;
  position: relative;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.progress-bar-fill.indeterminate {
  width: 100% !important;
  background: linear-gradient(90deg, #667eea, #764ba2);
  animation: indeterminate 2s ease-in-out infinite;
}

@keyframes indeterminate {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-bar-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Progress Text */
.progress-text {
  text-align: center;
  margin-bottom: 16px;
}

.progress-percentage {
  font-size: 32px;
  font-weight: 700;
  color: #28a745;
  margin-bottom: 8px;
  animation: countUp 0.6s ease-out;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.progress-status {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Progress Info */
.progress-info {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
  margin-top: 16px;
}

.time-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.elapsed-time,
.estimated-time {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-weight: 500;
}

.elapsed-time {
  color: #17a2b8;
}

.estimated-time {
  color: #fd7e14;
}

/* Responsive Design */
@media (max-width: 480px) {
  .progress-modal {
    min-width: 320px;
    margin: 16px;
  }

  .progress-header {
    padding: 16px 20px;
  }

  .progress-title {
    font-size: 16px;
  }

  .progress-content {
    padding: 24px 20px 20px;
  }

  .progress-percentage {
    font-size: 28px;
  }

  .time-info {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .progress-modal {
    background: #2d3748;
    color: #e2e8f0;
  }

  .progress-bar-track {
    background: #4a5568;
  }

  .progress-status {
    color: #a0aec0;
  }

  .progress-info {
    border-top-color: #4a5568;
  }

  .elapsed-time,
  .estimated-time {
    background: #4a5568;
    color: #e2e8f0;
  }

  .time-info {
    color: #a0aec0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .progress-bar-fill {
    background: #000;
  }

  .progress-percentage {
    color: #000;
  }

  .progress-header {
    background: #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .progress-overlay,
  .progress-modal,
  .progress-bar-fill,
  .progress-percentage,
  .checkmark-icon {
    animation: none;
  }

  .progress-bar-fill {
    transition: width 0.3s ease;
  }
}
</style>
