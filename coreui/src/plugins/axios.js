import axios from 'axios';
import { storePromise } from '../store';

// Configuration object for the plugin
const CONFIG = {
  SSE_PARAM_NAME: 'SSERequestId',
  REQUEST_TIMEOUT: 30 * 1000 * 60 * 60,
  MAX_RETRIES: 0,
  RETRY_DELAY: 1000,
  DEBUG: process.env.NODE_ENV === 'development'
};
// Plugin state
class AxiosSSEPlugin {
  constructor() {
    this.sseProgressService = null;
    this.isInitialized = false;
    this.pendingRequests = new Map();
    this.interceptorIds = { request: null, response: null };
  }

  /**
   * Initialize the plugin with store and SSE service
   */
  async init() {
    if (this.isInitialized) {
      this.log('Plugin already initialized');
      return;
    }

    try {
      const store = await storePromise;
      
      // Dynamically import SSE service to avoid circular dependencies
      const { default: SSEProgressService } = await import('../services/SSEProgressService');
      this.sseProgressService = new SSEProgressService(store);
      this.store = store;

      this.setupInterceptors();
      this.isInitialized = true;
      
      this.log('Plugin initialized successfully');
    } catch (error) {
      this.error('Failed to initialize plugin:', error);
      throw new Error(`AxiosSSEPlugin initialization failed: ${error.message}`);
    }
  }

  /**
   * Setup axios interceptors
   */
  setupInterceptors() {
    // Request interceptor
    this.interceptorIds.request = axios.interceptors.request.use(
      (config) => this.handleRequest(config),
      (error) => this.handleRequestError(error)
    );

    // Response interceptor
    this.interceptorIds.response = axios.interceptors.response.use(
      (response) => this.handleResponse(response),
      (error) => this.handleResponseError(error)
    );
  }

  /**
   * Handle outgoing requests
   */
  handleRequest(config) {
    try {
      // Validate configuration
      if (!config) {
        throw new Error('Request config is required');
      }

      // Set default timeout if not specified
      if (!config.timeout) {
        config.timeout = CONFIG.REQUEST_TIMEOUT;
      }

      // Check if this request should use SSE progress tracking
      if (this.shouldUseSSEProgress(config)) {
        return this.setupSSERequest(config);
      } else {
        return this.setupFallbackRequest(config);
      }
    } catch (error) {
      this.error('Error in request handler:', error);
      return this.handleRequestError(error);
    }
  }

  /**
   * Determine if request should use SSE progress tracking
   */
  shouldUseSSEProgress(config) {
    return config.useSSEProgress === true && 
           this.sseProgressService && 
           this.isInitialized;
  }

  /**
   * Setup SSE progress tracking for request
   */
  setupSSERequest(config) {
    // Check if this is a retry (already has a base request ID)
    let requestId;
    let isRetry = false;
    
    if (config._baseRequestId) {
      // This is a retry, reuse the base request ID and existing tracking info
      requestId = config._baseRequestId;
      isRetry = true;
    } else {
      // Generate a unique request ID for new requests
      requestId = this.generateRequestId(config);
      config._baseRequestId = requestId; // Store base ID for retries
    }
    
    config.requestId = requestId;

    // Add SSE request ID to params
    config.params = {
      ...(config.params || {}),
      [CONFIG.SSE_PARAM_NAME]: requestId,
    };

    // Track the request (only create new entry if not a retry)
    if (!isRetry) {
      this.pendingRequests.set(requestId, {
        timestamp: Date.now(),
        method: config.method,
        url: config.url,
        retries: 0
      });

      // Start SSE progress tracking for new requests only
      try {
        this.sseProgressService.startProgress(requestId, {
          method: config.method,
          url: config.url,
          timestamp: Date.now()
        });
        this.log(`Started SSE progress for request: ${requestId}`);
      } catch (error) {
        this.error('Failed to start SSE progress:', error);
        // Continue with request even if SSE fails
      }
    } else {
      this.log(`Reusing SSE progress for retry: ${requestId}`);
    }

    return config;
  }

  /**
   * Setup fallback progress tracking
   */
  setupFallbackRequest(config) {
    if (this.store) {
      this.store.dispatch("app/loadProgressBar");
    }
    return config;
  }

  /**
   * Generate unique request ID
   */
  generateRequestId(config) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const method = (config.method || 'GET').toUpperCase();
    const urlHash = this.hashCode(config.url || '');
    
    return `${method}_${urlHash}_${timestamp}_${random}`;
  }

  /**
   * Simple hash function for URLs
   */
  hashCode(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Handle successful responses
   */
  handleResponse(response) {
    try {
      const requestId = response.config?.requestId;
      
      if (requestId && this.sseProgressService) {
        this.stopSSEProgress(requestId);
      } else if (this.store) {
        this.store.dispatch("app/unLoadProgressBar");
      }

      return response;
    } catch (error) {
      this.error('Error in response handler:', error);
      return response; // Return response even if cleanup fails
    }
  }

  /**
   * Handle request errors
   */
  handleRequestError(error) {
    this.error('Request error:', error);
    this.cleanupProgress();
    return Promise.reject(error);
  }

  /**
   * Handle response errors
   */
  handleResponseError(error) {
    try {
      const requestId = error.config?.requestId;
      
      if (requestId) {
        const requestInfo = this.pendingRequests.get(requestId);
        
        // Check if we should retry
        if (this.shouldRetry(error, requestInfo)) {
          return this.retryRequest(error.config, requestInfo);
        }
        
        this.stopSSEProgress(requestId);
      } else {
        this.cleanupProgress();
      }

      this.error('Response error:', error);
      return Promise.reject(error);
    } catch (handlerError) {
      this.error('Error in response error handler:', handlerError);
      return Promise.reject(error);
    }
  }

  /**
   * Check if request should be retried
   */
  shouldRetry(error, requestInfo) {
    if (!requestInfo || requestInfo.retries >= CONFIG.MAX_RETRIES) {
      return false;
    }

    // Retry on network errors or 5xx server errors
    const status = error.response?.status;
    return !status || (status >= 500 && status < 600);
  }

  /**
   * Retry failed request
   */
  async retryRequest(config, requestInfo) {
    requestInfo.retries++;
    
    this.log(`Retrying request ${config.requestId} (attempt ${requestInfo.retries}/${CONFIG.MAX_RETRIES})`);
    
    // Wait before retrying
    await this.delay(CONFIG.RETRY_DELAY * requestInfo.retries);
    
    // Preserve the base request ID for retry tracking
    // The setupSSERequest method will recognize this as a retry
    const retryConfig = {
      ...config,
      _baseRequestId: config._baseRequestId || config.requestId
    };
    
    return axios(retryConfig);
  }

  /**
   * Stop SSE progress tracking
   */
  stopSSEProgress(requestId) {
    try {
      if (this.sseProgressService && requestId) {
        this.sseProgressService.stopProgress(requestId);
        this.pendingRequests.delete(requestId);
        this.log(`Stopped SSE progress for request: ${requestId}`);
      }
    } catch (error) {
      this.error('Error stopping SSE progress:', error);
    }
  }

  /**
   * Generic progress cleanup
   */
  cleanupProgress() {
    try {
      if (this.sseProgressService) {
        this.sseProgressService.stopProgress();
      }
      
      if (this.store) {
        this.store.dispatch("app/unLoadProgressBar");
      }
    } catch (error) {
      this.error('Error cleaning up progress:', error);
    }
  }

  /**
   * Get SSE service instance
   */
  getSSEProgressService() {
    if (!this.isInitialized) {
      this.warn('Plugin not initialized yet');
      return null;
    }
    return this.sseProgressService;
  }

  /**
   * Get plugin statistics
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      pendingRequests: this.pendingRequests.size,
      hasSSEService: !!this.sseProgressService,
      interceptorsActive: {
        request: this.interceptorIds.request !== null,
        response: this.interceptorIds.response !== null
      }
    };
  }

  /**
   * Cleanup and destroy plugin
   */
  destroy() {
    try {
      // Remove interceptors
      if (this.interceptorIds.request !== null) {
        axios.interceptors.request.eject(this.interceptorIds.request);
      }
      if (this.interceptorIds.response !== null) {
        axios.interceptors.response.eject(this.interceptorIds.response);
      }

      // Clean up pending requests
      this.pendingRequests.clear();
      
      // Reset state
      this.sseProgressService = null;
      this.store = null;
      this.isInitialized = false;
      this.interceptorIds = { request: null, response: null };
      
      this.log('Plugin destroyed successfully');
    } catch (error) {
      this.error('Error destroying plugin:', error);
    }
  }

  // Utility methods
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  log(...args) {
    if (CONFIG.DEBUG) {
      console.log('[AxiosSSEPlugin]', ...args);
    }
  }

  warn(...args) {
    if (CONFIG.DEBUG) {
      console.warn('[AxiosSSEPlugin]', ...args);
    }
  }

  error(...args) {
    console.error('[AxiosSSEPlugin]', ...args);
  }
}

// Create plugin instance
const axiosSSEPlugin = new AxiosSSEPlugin();

// Make axios globally available
window.axios = axios;

// Initialize plugin
axiosSSEPlugin.init().catch(error => {
  console.error('Failed to initialize AxiosSSEPlugin:', error);
});

// Export functions for external use
export const getSSEProgressService = () => axiosSSEPlugin.getSSEProgressService();
export const getPluginStats = () => axiosSSEPlugin.getStats();
export const destroyPlugin = () => axiosSSEPlugin.destroy();

// Export plugin instance for advanced usage
export { axiosSSEPlugin };

// Export configuration for customization
export { CONFIG };